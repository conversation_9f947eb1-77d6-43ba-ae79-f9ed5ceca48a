"use client";

import React from "react";
import { motion } from "framer-motion";
import { Navigation } from "@/components/navigation";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { BookOpen, Lightbulb, Users, ArrowRight } from "lucide-react";
import { Footer } from "@/components/footer";

const programs = [
  {
    id: "afterschool",
    title: "Afterschool Program",
    description: "Comprehensive afterschool program providing academic support, enrichment activities, and safe learning environment throughout the year",
    icon: BookOpen,
    features: [
      "Homework assistance and tutoring",
      "STEM activities and projects",
      "Arts and creative expression",
      "Physical activities and sports",
      "Social skills development",
      "Healthy snacks provided",
      "Safe and supervised environment",
      "Character building activities"
    ],
    ageRange: "Ages 5-14",
    duration: "Year-round program",
    schedule: "Monday-Friday, 3:00 PM - 6:00 PM",
    location: "WLC Academy Campus"
  }
];

export default function ProgramsPage() {
  return (
    <div className="min-h-screen bg-white">
      <Navigation />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-6xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <div className="inline-block px-4 py-2 bg-[#F3CC5C] rounded-full text-sm font-medium text-[#07243C] mb-6">
              Our Programs
            </div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-light text-gray-900 mb-6 leading-tight">
              Afterschool Program
            </h1>
            <p className="text-lg md:text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto">
              Year-round afterschool program providing academic support, enrichment activities, and a safe learning environment for students
            </p>
          </motion.div>
        </div>
      </section>

      {/* Program Details */}
      <section className="pb-24 bg-white">
        <div className="max-w-4xl mx-auto px-6">
          <div className="grid grid-cols-1 gap-8">
            {programs.map((program, index) => {
              const IconComponent = program.icon;
              return (
                <motion.div
                  key={program.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.2 }}
                  id={program.id}
                >
                  <Card className="h-full p-8 border border-gray-100 hover:border-[#F3CC5C] transition-all duration-200 hover:shadow-lg">
                    <CardContent className="p-0">
                      <div className="w-20 h-20 bg-[#F3CC5C] rounded-xl flex items-center justify-center mb-8 mx-auto">
                        <IconComponent className="w-10 h-10 text-[#07243C]" />
                      </div>

                      <h3 className="text-3xl font-medium text-[#07243C] mb-4 text-center">{program.title}</h3>
                      <p className="text-gray-600 leading-relaxed mb-8 text-center text-lg">{program.description}</p>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                        <div className="space-y-4">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-500">Age Range:</span>
                            <span className="font-medium text-gray-900">{program.ageRange}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-500">Duration:</span>
                            <span className="font-medium text-gray-900">{program.duration}</span>
                          </div>
                        </div>
                        <div className="space-y-4">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-500">Schedule:</span>
                            <span className="font-medium text-gray-900">{program.schedule}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-500">Location:</span>
                            <span className="font-medium text-gray-900">{program.location}</span>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-3 mb-8">
                        <h4 className="font-medium text-gray-900 mb-4 text-lg">Program Features:</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                          {program.features.map((feature, featureIndex) => (
                            <div key={featureIndex} className="flex items-center text-sm text-gray-600">
                              <div className="w-2 h-2 bg-[#F3CC5C] rounded-full mr-3 flex-shrink-0"></div>
                              {feature}
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="flex flex-col sm:flex-row gap-4">
                        <Button className="flex-1 bg-[#07243C] text-white hover:bg-[#0a2d47] group">
                          Enroll Now
                          <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                        </Button>
                        <Button variant="outline" className="flex-1 text-[#07243C] border-[#07243C] hover:bg-[#07243C] hover:text-white">
                          Contact Us
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Admission Process */}
      {/* <section className="py-24 bg-gray-50">
        <div className="max-w-6xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-light text-gray-900 mb-4">
              Admission Process
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Simple steps to join our community of learners
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              { step: "01", title: "Application", description: "Submit your application form online" },
              { step: "02", title: "Assessment", description: "Participate in our assessment process" },
              { step: "03", title: "Interview", description: "Meet with our admissions team" },
              { step: "04", title: "Enrollment", description: "Complete enrollment and begin your journey" }
            ].map((step, index) => (
              <motion.div
                key={step.step}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-[#07243C] text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-medium">
                  {step.step}
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">{step.title}</h3>
                <p className="text-gray-600 text-sm">{step.description}</p>
              </motion.div>
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="text-center mt-12"
          >
            <Button size="lg" className="bg-[#07243C] text-white hover:bg-[#0a2d47]">
              Start Application
            </Button>
          </motion.div>
        </div>
      </section> */}

      <Footer />
    </div>
  );
}
