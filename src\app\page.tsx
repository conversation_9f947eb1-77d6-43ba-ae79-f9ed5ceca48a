"use client";

import React from "react";
import { motion } from "framer-motion";
import { Navigation } from "@/components/navigation";
import { HeroSection } from "@/components/hero-section";
import { GallerySection } from "@/components/gallery-section";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import Image from "next/image";
import Link from "next/link";
import { BookOpen, Users, Lightbulb, MapPin, Phone, Mail, ArrowRight } from "lucide-react";
import { Footer } from "@/components/footer";

export default function Home() {

  return (
    <div className="min-h-screen bg-white">
      <Navigation />
      <HeroSection />

      {/* Features Section */}
      <section className="py-24 bg-gray-50">
        <div className="max-w-6xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-light text-gray-900 mb-4">
              Why Choose WLC Academy
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Three pillars that define our commitment to excellence
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                icon: BookOpen,
                title: "Academic Excellence",
                description: "Innovative curriculum designed to challenge minds and inspire breakthrough thinking",
                bgColor: "bg-[#F3CC5C]",
                iconColor: "text-[#07243C]"
              },
              {
                icon: Users,
                title: "Expert Mentors",
                description: "World-class educators dedicated to nurturing potential and fostering growth",
                bgColor: "bg-[#07243C]",
                iconColor: "text-[#F3CC5C]"
              },
              {
                icon: Lightbulb,
                title: "Future Ready",
                description: "Cutting-edge facilities and technology preparing students for tomorrow's challenges",
                bgColor: "bg-[#F3CC5C]",
                iconColor: "text-[#07243C]"
              }
            ].map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.2 }}
                >
                  <Card className="text-center p-8 border border-gray-100 hover:border-[#F3CC5C] transition-colors duration-200">
                    <CardContent className="p-0">
                      <div className={`w-16 h-16 ${feature.bgColor} rounded-xl flex items-center justify-center mx-auto mb-6`}>
                        <IconComponent className={`w-8 h-8 ${feature.iconColor}`} />
                      </div>
                      <h3 className="text-xl font-medium text-[#07243C] mb-3">{feature.title}</h3>
                      <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      <GallerySection />

      {/* About Section */}
      <section className="py-24 bg-white">
        <div className="max-w-6xl mx-auto px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <div className="mb-8">
                <div className="inline-block px-4 py-2 bg-[#F3CC5C] rounded-full text-sm font-medium text-[#07243C] mb-6">
                  About WLC Academy
                </div>
                <h2 className="text-3xl md:text-4xl font-light text-gray-900 mb-6 leading-tight">
                  Shaping minds for a brighter tomorrow
                </h2>
              </div>

              <p className="text-lg text-gray-600 leading-relaxed mb-6">
                Founded with a vision to redefine education, WLC Academy stands at the intersection
                of innovation and excellence. We don't just teach—we inspire, challenge, and
                transform lives.
              </p>

              <p className="text-gray-600 leading-relaxed mb-8">
                Our approach goes beyond traditional learning, fostering critical thinking,
                creativity, and leadership skills that prepare students for an ever-evolving world.
              </p>

              <Button className="bg-[#07243C] text-white hover:bg-[#0a2d47]">
                <Link href="/about">Discover Our Story</Link>
              </Button>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="relative"
            >
              <Card className="p-8 border border-gray-100">
                <CardContent className="p-0">
                  <div className="grid grid-cols-1 gap-8">
                    <div className="text-center">
                      <h3 className="text-xl font-medium text-gray-900 mb-2">Our Mission</h3>
                      <p className="text-gray-600 text-sm leading-relaxed">
                        Empowering students to become confident, capable leaders who shape the future
                      </p>
                    </div>
                    <div className="text-center">
                      <h3 className="text-xl font-medium text-gray-900 mb-2">Our Vision</h3>
                      <p className="text-gray-600 text-sm leading-relaxed">
                        Setting the global standard for educational excellence and innovation
                      </p>
                    </div>
                  </div>

                  <div className="mt-8 pt-8 border-t border-gray-200">
                    <div className="grid grid-cols-2 gap-8 text-center">
                      <div>
                        <div className="text-2xl font-medium text-gray-900 mb-1">2025</div>
                        <div className="text-sm text-gray-600">Founded</div>
                      </div>
                      <div>
                        <div className="text-2xl font-medium text-gray-900 mb-1">Global</div>
                        <div className="text-sm text-gray-600">Recognition</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>



      {/* Programs Section */}
      <section className="py-24 bg-white">
        <div className="max-w-6xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <div className="inline-block px-4 py-2 bg-[#F3CC5C] rounded-full text-sm font-medium text-[#07243C] mb-6">
              Our Program
            </div>
            <h2 className="text-3xl md:text-4xl font-light text-gray-900 mb-4">
              Afterschool Excellence
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Year-round afterschool program providing academic support, enrichment activities, and a safe learning environment
            </p>
          </motion.div>

          <div className="max-w-2xl mx-auto">
            {[
              {
                icon: BookOpen,
                title: "Afterschool Program",
                description: "Comprehensive year-round afterschool program providing academic support, enrichment activities, and a safe learning environment for students ages 5-14",
                features: [
                  "Homework assistance and tutoring",
                  "STEM activities and creative arts",
                  "Physical activities and character building",
                  "Healthy snacks and safe environment"
                ],
                bgColor: "bg-[#F3CC5C]",
                iconColor: "text-[#07243C]",
                schedule: "Monday-Friday, 3:00 PM - 6:00 PM"
              }
            ].map((program, index) => {
              const IconComponent = program.icon;
              return (
                <motion.div
                  key={program.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.2 }}
                >
                  <Card className="p-8 border border-gray-100 hover:border-[#F3CC5C] transition-colors duration-200 h-full text-center">
                    <CardContent className="p-0">
                      <div className={`w-20 h-20 ${program.bgColor} rounded-xl flex items-center justify-center mb-6 mx-auto`}>
                        <IconComponent className={`w-10 h-10 ${program.iconColor}`} />
                      </div>
                      <h3 className="text-2xl font-medium text-[#07243C] mb-4">{program.title}</h3>
                      <p className="text-gray-600 leading-relaxed mb-6">{program.description}</p>

                      <div className="bg-gray-50 rounded-lg p-4 mb-6">
                        <p className="text-sm font-medium text-[#07243C] mb-2">Program Schedule</p>
                        <p className="text-sm text-gray-600">{program.schedule}</p>
                      </div>

                      <div className="space-y-2 mb-8 text-left">
                        <h4 className="font-medium text-gray-900 mb-3">Key Features:</h4>
                        {program.features.map((feature, featureIndex) => (
                          <div key={featureIndex} className="flex items-center text-sm text-gray-600">
                            <div className="w-2 h-2 bg-[#F3CC5C] rounded-full mr-3 flex-shrink-0"></div>
                            {feature}
                          </div>
                        ))}
                      </div>

                      <div className="flex flex-col sm:flex-row gap-3">
                        <Button className="flex-1 bg-[#07243C] text-white hover:bg-[#0a2d47] group">
                          <Link href="/programs">Learn More</Link>
                          <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                        </Button>
                        <Button variant="outline" className="flex-1 text-[#07243C] border-[#07243C] hover:bg-[#07243C] hover:text-white">
                          <Link href="/contact">Enroll Now</Link>
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-24 bg-gray-50">
        <div className="max-w-6xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <div className="inline-block px-4 py-2 bg-[#F3CC5C] rounded-full text-sm font-medium text-[#07243C] mb-6">
              Get in Touch
            </div>
            <h2 className="text-3xl md:text-4xl font-light text-gray-900 mb-4">
              Ready to begin your journey?
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Take the first step towards excellence. We're here to guide you every step of the way.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="space-y-8"
            >
              <div>
                <h3 className="text-xl font-medium text-gray-900 mb-6">Connect With Us</h3>
                <div className="space-y-6">
                  <div className="flex items-start">
                    <div className="w-12 h-12 bg-[#F3CC5C] rounded-xl flex items-center justify-center mr-4 flex-shrink-0">
                      <MapPin className="w-5 h-5 text-[#07243C]" />
                    </div>
                    <div>
                      <div className="text-[#07243C] font-medium mb-1">Visit Us</div>
                      <div className="text-gray-600">123 Education Street, Learning City</div>
                      <div className="text-gray-600">LC 12345, Country</div>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-12 h-12 bg-[#07243C] rounded-xl flex items-center justify-center mr-4 flex-shrink-0">
                      <Phone className="w-5 h-5 text-[#F3CC5C]" />
                    </div>
                    <div>
                      <div className="text-[#07243C] font-medium mb-1">Call Us</div>
                      <div className="text-gray-600">(*************</div>
                      <div className="text-gray-600">(*************</div>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-12 h-12 bg-[#F3CC5C] rounded-xl flex items-center justify-center mr-4 flex-shrink-0">
                      <Mail className="w-5 h-5 text-[#07243C]" />
                    </div>
                    <div>
                      <div className="text-[#07243C] font-medium mb-1">Email Us</div>
                      <div className="text-gray-600"><EMAIL></div>
                      <div className="text-gray-600"><EMAIL></div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <Card className="p-8 border border-gray-100">
                <CardContent className="p-0">
                  <h3 className="text-xl font-medium text-gray-900 mb-6">Send us a message</h3>
                  <form className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Input
                          type="text"
                          placeholder="First Name"
                          className="w-full"
                        />
                      </div>
                      <div>
                        <Input
                          type="text"
                          placeholder="Last Name"
                          className="w-full"
                        />
                      </div>
                    </div>
                    <div>
                      <Input
                        type="email"
                        placeholder="Email Address"
                        className="w-full"
                      />
                    </div>
                    <div>
                      <Textarea
                        rows={5}
                        placeholder="Tell us about your interest in WLC Academy..."
                        className="w-full resize-none"
                      />
                    </div>
                    <Button
                      type="submit"
                      className="w-full bg-[#07243C] text-white hover:bg-[#0a2d47]"
                    >
                      Send Message
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-center mt-16"
          >
            <p className="text-gray-600 mb-4">
              Want to explore more? Visit our dedicated pages for detailed information.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <Button variant="outline" className="text-[#07243C] border-[#07243C] hover:bg-[#07243C] hover:text-white">
                <Link href="/about">Learn About Us</Link>
              </Button>
              <Button variant="outline" className="text-[#07243C] border-[#07243C] hover:bg-[#07243C] hover:text-white">
                <Link href="/programs">View Programs</Link>
              </Button>
              <Button className="bg-[#07243C] text-white hover:bg-[#0a2d47]">
                <Link href="/contact">Full Contact Page</Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
